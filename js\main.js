/**
 * 主程序入口
 */
// 引入第三方库
import * as echarts from "echarts";
import * as d3 from "d3";
import dayjs from "dayjs";

// 引入组件
import CPSChart from "./components/CPSChart.js";
import InfoDisplay from "./components/InfoDisplay.js";
import SectionTable from "./components/SectionTable.js";

// 将库挂载到全局对象，保持与原有代码的兼容性
window.echarts = echarts;
window.d3 = d3;
window.dayjs = dayjs;

/**
 * 设置三个iframe的地址
 * 从环境变量中获取URL并设置到对应的iframe
 */
function setupIframeUrls() {
  // 获取环境变量中的URL
  // const balanceUrl = import.meta.env.VITE_CHART_BALANCE;
  // const windUrl = import.meta.env.VITE_CHART_WIND;
  // const solarUrl = import.meta.env.VITE_CHART_SOLAR;
  const mainUrl = import.meta.env.VITE_CHART_URL;

  // // 获取iframe元素
  // const balanceIframe = document.querySelector(".balance-chart-layout iframe");
  // const windIframe = document.querySelector(".wind-chart-layout iframe");
  // const solarIframe = document.querySelector(".solar-chat-layout iframe");
  const mainIframe = document.querySelector(".top-layout iframe");

  // 设置平衡图表iframe的src属性
  // if (balanceIframe && balanceUrl) {
  //   balanceIframe.src = balanceUrl;
  //   console.log("平衡图表iframe地址已设置:", balanceUrl);
  // } else {
  //   console.warn("平衡图表iframe或URL未找到", {
  //     iframe: !!balanceIframe,
  //     url: balanceUrl,
  //   });
  // }

  // // 设置风电图表iframe的src属性
  // if (windIframe && windUrl) {
  //   windIframe.src = windUrl;
  //   console.log("风电图表iframe地址已设置:", windUrl);
  // } else {
  //   console.warn("风电图表iframe或URL未找到", {
  //     iframe: !!windIframe,
  //     url: windUrl,
  //   });
  // }

  // // 设置光伏图表iframe的src属性
  // if (solarIframe && solarUrl) {
  //   solarIframe.src = solarUrl;
  //   console.log("光伏图表iframe地址已设置:", solarUrl);
  // } else {
  //   console.warn("光伏图表iframe或URL未找到", {
  //     iframe: !!solarIframe,
  //     url: solarUrl,
  //   });
  // }

  if (mainIframe && mainUrl) {
    mainIframe.src = mainUrl;
    console.log("主图表iframe地址已设置:", mainUrl);
  } else {
    console.warn("主图表iframe或URL未找到", {
      iframe: !!mainIframe,
      url: mainUrl,
    });
  }
}

// 页面加载完成后初始化所有图表
document.addEventListener("DOMContentLoaded", () => {
  // 存储所有组件实例
  const components = {};

  // 设置三个iframe的地址
  setupIframeUrls();

  // 初始化平衡曲线图表
  // components.balanceChart = new BalanceChart("balance-chart", data.balanceData);

  // 初始化风电消纳曲线图表
  // components.windChart = new WindChart("wind-chart", data.windData);

  // 初始化光伏消纳曲线图表
  // components.solarChart = new SolarChart("solar-chart", data.solarData);

  // 初始化断面监视表格
  components.sectionTable = new SectionTable("section-table");

  // 初始化CPS曲线图表（不传入静态数据，让组件自动从API获取并启动定时刷新）
  components.cpsChart = new CPSChart("cps-chart");

  // 初始化信息显示（不传入静态数据，让组件自动从API获取）
  components.infoDisplay = new InfoDisplay("info-container");

  // 将组件实例挂载到全局对象，便于调试和控制
  window.balanceMonitorComponents = components;

  // 添加iframe管理功能到全局对象
  window.balanceMonitorComponents.iframeManager = {
    // 重新设置iframe地址
    refreshIframes: setupIframeUrls,
    // 获取当前iframe状态
    getIframeStatus: () => {
      const balanceIframe = document.querySelector(
        ".balance-chart-layout iframe"
      );
      const windIframe = document.querySelector(".wind-chart-layout iframe");
      const solarIframe = document.querySelector(".solar-chat-layout iframe");

      return {
        balance: { element: !!balanceIframe, src: balanceIframe?.src || null },
        wind: { element: !!windIframe, src: windIframe?.src || null },
        solar: { element: !!solarIframe, src: solarIframe?.src || null },
      };
    },
  };

  // 添加CPS图表实时数据管理功能
  window.balanceMonitorComponents.cpsRealtimeManager = {
    // 手动获取实时数据
    fetchRealtimeData: () => components.cpsChart?.fetchRealtimeData(),
    // 获取当前实时数据
    getRealtimeData: () => components.cpsChart?.realtimeData || {},
    // 设置实时数据获取间隔
    setRealtimeInterval: (intervalMs) =>
      components.cpsChart?.setRealtimeInterval(intervalMs),
    // 启动实时数据获取
    startRealtimeDataFetch: () => components.cpsChart?.startRealtimeDataFetch(),
    // 停止实时数据获取
    stopRealtimeDataFetch: () => components.cpsChart?.stopRealtimeDataFetch(),
  };

  // 添加时间格式化测试功能
  window.balanceMonitorComponents.testTimeFormat = () => {
    if (components.cpsChart) {
      const testTimes = [
        "2025-06-17 00:00:00", // CPS接口返回的标准格式
        "2025-06-23 14:30:15",
        "2025-06-23 14:30:30",
        "2025-06-23 14:30:45",
        "2025-06-23 14:31:00",
        "2025-12-31 23:59:59",
      ];

      console.log("=== CPS图表时间格式化测试（使用dayjs，精确到秒）===");
      testTimes.forEach((time) => {
        const formatted = components.cpsChart.formatTimeDisplay(time);
        console.log(`${time} -> ${formatted}`);
      });

      console.log(
        "当前x轴数据示例:",
        components.cpsChart.data?.xAxis?.slice(-3)
      );
    } else {
      console.log("CPS图表未初始化");
    }
  };

  console.log("全网平衡监视系统初始化完成", components);
  console.log(
    "iframe状态:",
    window.balanceMonitorComponents.iframeManager.getIframeStatus()
  );
  console.log("CPS实时数据管理器已初始化");
  console.log(
    "💡 使用 window.balanceMonitorComponents.testTimeFormat() 测试时间格式化功能"
  );
});
