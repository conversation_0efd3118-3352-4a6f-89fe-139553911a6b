/**
 * CPS曲线图表组件
 */
import dayjs from "dayjs";
import {
  processSimpleSeries,
  createChartOption,
  buildCPSDataUrl,
  buildRealtimeCPSDataUrl,
  getDefaultCPSDateRange,
  ColorOptions,
} from "../config/ChartConfig.js";

class CPSChart {
  constructor(containerId, data = null, dateRange = null) {
    console.log("🚀 ~ CPSChart ~ constructor ~ data:", data);
    this.container = document.getElementById(containerId);
    this.data = data;
    this.chart = null;
    this.dateRange = dateRange || getDefaultCPSDateRange();
    this.apiUrl = buildCPSDataUrl(
      this.dateRange.startDay,
      this.dateRange.endDay
    );
    this.realtimeApiUrl = buildRealtimeCPSDataUrl(); // 实时数据API URL
    this.refreshTimer = null; // 定时器
    this.refreshInterval = 60000; // 刷新间隔：60秒（1分钟）
    this.realtimeTimer = null; // 实时数据定时器
    this.realtimeInterval = 5000; // 实时数据刷新间隔：5秒
    this.realtimeData = { CPS1: null, CPS2: null }; // 存储最新的实时数据
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 初始化ECharts实例
    this.chart = echarts.init(this.container);

    // 如果没有传入数据，则从接口获取
    if (!this.data) {
      this.fetchData();
    } else {
      this.data = this.transformApiData(this.data);
      this.render();
    }

    // 启动定时刷新
    this.startAutoRefresh();

    // 启动实时数据获取
    this.startRealtimeDataFetch();

    // 添加窗口大小变化的监听器
    window.addEventListener("resize", () => {
      this.chart.resize();
    });
  }

  // 从接口获取数据
  async fetchData() {
    try {
      const response = await fetch(this.apiUrl);
      const result = await response.json();

      if (result.code === "0000" && result.data) {
        // 转换接口数据为图表格式
        this.data = this.transformApiData(result.data);
        this.render();
      } else {
        console.error("CPS接口返回错误:", result);
        // this.showError("数据获取失败");
      }
    } catch (error) {
      console.error("CPS接口调用失败:", error);
      // this.showError("网络请求失败");
    }
  }

  // 获取实时CPS数据
  async fetchRealtimeData() {
    try {
      const response = await fetch(this.realtimeApiUrl);
      const result = await response.json();

      if (result.code === "0000" && result.data) {
        this.processRealtimeData(result.data);
      } else {
        console.error("实时CPS接口返回错误:", result);
      }
    } catch (error) {
      console.error("实时CPS接口调用失败:", error);
    }
  }

  // 处理实时数据
  processRealtimeData(realtimeApiData) {
    // 更新实时数据存储
    if (Array.isArray(realtimeApiData)) {
      this.realtimeData.CPS1 = {
        value: this.formatNumber(200, realtimeApiData[0]),
        time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      };
      this.realtimeData.CPS2 = {
        value: this.formatNumber(23, realtimeApiData[1]),
        time: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      };

      // 将实时数据插入到历史数据中
      this.insertRealtimeDataToHistory();

      // 重新渲染图表
      this.render();

      console.log("实时CPS数据已更新:", this.realtimeData);
    }
  }

  // 将实时数据插入到历史数据中
  insertRealtimeDataToHistory() {
    if (!this.data || !this.data.series) return;

    // 使用dayjs生成当前时间字符串，格式与CPS接口返回格式一致
    const timeString = dayjs().format("YYYY-MM-DD HH:mm:ss");
    const displayTime = this.formatTimeDisplay(timeString);

    // 检查是否需要添加新的时间点
    if (!this.data.xAxis.includes(displayTime)) {
      this.data.xAxis.push(displayTime);

      // 为每个系列添加新的数据点
      this.data.series.forEach((series, index) => {
        if (index === 0 && this.realtimeData.CPS1) {
          // CPS1系列
          series.data.push(this.realtimeData.CPS1.value);
        } else if (index === 1 && this.realtimeData.CPS2) {
          // CPS2系列
          series.data.push(this.realtimeData.CPS2.value);
        } else {
          // 其他系列添加null值
          series.data.push(null);
        }
      });
    } else {
      // 更新现有时间点的数据
      const timeIndex = this.data.xAxis.indexOf(displayTime);
      if (timeIndex !== -1) {
        this.data.series.forEach((series, index) => {
          if (index === 0 && this.realtimeData.CPS1) {
            series.data[timeIndex] = this.realtimeData.CPS1.value;
          } else if (index === 1 && this.realtimeData.CPS2) {
            series.data[timeIndex] = this.realtimeData.CPS2.value;
          }
        });
      }
    }
  }

  // 转换接口数据为图表格式
  transformApiData(apiData) {
    const { CPS1List = [], CPS2List = [] } = apiData;

    // 提取所有时间点，去重并排序作为X轴
    const allTimes = [
      ...CPS1List.map((item) => item.time),
      ...CPS2List.map((item) => item.time),
    ];
    const uniqueTimes = [...new Set(allTimes)].sort();

    // 格式化时间显示，只显示月份和时间，去除年份
    const xAxis = uniqueTimes.map((time) => this.formatTimeDisplay(time));

    // 创建时间到值的映射
    const timeValueMap1 = new Map();
    const timeValueMap2 = new Map();

    CPS1List.forEach((item) => {
      timeValueMap1.set(item.time, this.formatNumber(item.value));
    });

    CPS2List.forEach((item) => {
      timeValueMap2.set(item.time, this.formatNumber(item.value));
    });

    // 根据X轴时间生成对应的Y轴数据
    const cps1Data = uniqueTimes.map((time) => timeValueMap1.get(time) || null);
    const cps2Data = uniqueTimes.map((time) => timeValueMap2.get(time) || null);

    return {
      xAxis,
      series: [
        {
          name: "当班CPS1",
          colorType: "green",
          showSymbol: true,
          data: cps1Data,
        },
        {
          name: "当班CPS2",
          colorType: "blue",
          showSymbol: true,
          data: cps2Data,
        },
      ],
    };
  }

  // 格式化时间显示，只显示月份和时间，去除年份，精确到秒
  formatTimeDisplay(timeString) {
    // 输入格式: "2025-06-17 00:00:00"
    // 输出格式: "06-17 00:00:00"
    if (!timeString) return timeString;

    try {
      // 使用dayjs解析和格式化时间
      const date = dayjs(timeString);

      // 检查日期是否有效
      if (!date.isValid()) {
        console.warn("无效的时间格式:", timeString);
        return timeString;
      }

      // 返回月-日 时:分:秒格式
      return date.format("MM-DD HH:mm:ss");
    } catch (error) {
      console.warn("时间格式化失败:", timeString, error);
      return timeString;
    }
  }

  // 格式化数字为1位小数
  formatNumber(value) {
    if (value === null || value === undefined || value === "") {
      return null;
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
      return null;
    }

    return parseFloat(num.toFixed(1));
  }

  // 显示错误信息
  showError(message) {
    if (this.chart) {
      this.chart.showLoading({
        text: message,
        color: "#c23531",
        textColor: "#c23531",
        maskColor: "rgba(255, 255, 255, 0.8)",
        zlevel: 0,
      });
    }
  }

  drawRealtimeDataView(data, seriesData) {
    // === 计算最后一个点的位置（单位：像素） ===
    const lastIndex = data.length - 1;
    const lastCategory = data[lastIndex];
    const lastCPS1Value = seriesData[0].data[lastIndex];
    const lastCPS2Value = seriesData[1].data[lastIndex];

    // 等待图表渲染完成后再计算像素坐标
    setTimeout(() => {
      const pos1 = this.chart.convertToPixel({ seriesIndex: 0 }, [
        lastCategory,
        lastCPS1Value,
      ]);

      const pos2 = this.chart.convertToPixel({ seriesIndex: 1 }, [
        lastCategory,
        lastCPS2Value,
      ]);

      this.chart.setOption({
        graphic: {
          elements: [
            {
              type: "group",
              right: 0,
              top: pos1[1] * 0.3,
              children: [
                {
                  type: "rect",
                  z: 1000,
                  shape: {
                    width: 130,
                    height: 20,
                    r: 3,
                  },
                  style: {
                    fill: ColorOptions.green.colorTransparent2,
                    stroke: ColorOptions.green.color,
                    lineWidth: 2,
                    shadowBlur: 8,
                    shadowColor: "rgba(0,0,0)",
                  },
                },
                {
                  type: "text",
                  z: 1000,
                  left: 10,
                  top: 5,
                  style: {
                    text: `CPS1实时值: ${lastCPS1Value}`,
                    fill: ColorOptions.green.color,
                    font: "14px sans-serif",
                    textAlign: "center",
                    textVerticalAlign: "top",
                  },
                },
              ],
            },
            {
              type: "group",
              right: 0,
              top: pos2[1] * 1.2,
              children: [
                {
                  type: "rect",
                  z: 1000,
                  shape: {
                    width: 130,
                    height: 20,
                    r: 3,
                  },
                  style: {
                    fill: ColorOptions.blue.colorTransparent2,
                    stroke: ColorOptions.blue.color,
                    lineWidth: 2,
                    shadowBlur: 8,
                    shadowColor: "rgba(0,0,0)",
                  },
                },
                {
                  type: "text",
                  z: 1000,
                  left: 10,
                  top: 5,
                  style: {
                    text: `CPS2实时值: ${lastCPS1Value}`,
                    fill: ColorOptions.blue.color,
                    font: "14px sans-serif",
                    textAlign: "center",
                    textVerticalAlign: "top",
                  },
                },
              ],
            },
          ],
        },
      });
    }, 100);
  }

  render() {
    if (!this.data) {
      this.showError("数据格式错误");
      return;
    }

    // 隐藏加载状态
    if (this.chart) {
      this.chart.hideLoading();
    }

    const { xAxis, series, yAxis } = this.data;

    // 使用公共配置处理简单系列数据
    const seriesData = processSimpleSeries(series);

    // 使用公共配置创建图表选项
    const option = createChartOption({
      xAxis,
      series: seriesData,
      yAxis,
      gridConfig: {
        top: "20%",
        bottom: "15%", // 增加底部空间以容纳旋转的标签
      },
      legendConfig: {},
      xAxisConfig: {
        interval: "auto", // 自动计算间隔
      },
    });

    this.chart.setOption(option);
    this.drawRealtimeDataView(xAxis, seriesData);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 更新日期范围并重新获取数据
  updateDateRange(startDay, endDay) {
    this.dateRange = { startDay, endDay };
    this.apiUrl = buildCPSDataUrl(startDay, endDay);
    this.fetchData();
  }

  // 刷新数据（重新从接口获取）
  refreshData() {
    this.fetchData();
  }

  // 启动自动刷新
  startAutoRefresh() {
    // 清除现有定时器
    this.stopAutoRefresh();

    // 设置新的定时器
    this.refreshTimer = setInterval(() => {
      console.log("CPS图表自动刷新数据...");
      this.refreshData();
    }, this.refreshInterval);

    console.log(
      `CPS图表自动刷新已启动，间隔：${this.refreshInterval / 1000}秒`
    );
  }

  // 停止自动刷新
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
      console.log("CPS图表自动刷新已停止");
    }
  }

  // 设置刷新间隔
  setRefreshInterval(intervalMs) {
    this.refreshInterval = intervalMs;
    // 如果定时器正在运行，重新启动以应用新间隔
    if (this.refreshTimer) {
      this.startAutoRefresh();
    }
  }

  // 启动实时数据获取
  startRealtimeDataFetch() {
    // 清除现有定时器
    this.stopRealtimeDataFetch();

    // 立即获取一次实时数据
    this.fetchRealtimeData();

    // 设置定时器
    this.realtimeTimer = setInterval(() => {
      console.log("获取实时CPS数据...");
      this.fetchRealtimeData();
    }, this.realtimeInterval);

    console.log(
      `实时CPS数据获取已启动，间隔：${this.realtimeInterval / 1000}秒`
    );
  }

  // 停止实时数据获取
  stopRealtimeDataFetch() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer);
      this.realtimeTimer = null;
      console.log("实时CPS数据获取已停止");
    }
  }

  // 设置实时数据获取间隔
  setRealtimeInterval(intervalMs) {
    this.realtimeInterval = intervalMs;
    // 如果定时器正在运行，重新启动以应用新间隔
    if (this.realtimeTimer) {
      this.startRealtimeDataFetch();
    }
  }

  // 销毁图表
  destroy() {
    // 停止自动刷新
    this.stopAutoRefresh();

    // 停止实时数据获取
    this.stopRealtimeDataFetch();

    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.chart.resize);
  }
}

export default CPSChart;
