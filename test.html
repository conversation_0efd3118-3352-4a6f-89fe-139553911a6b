<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>平衡图表测试页面</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        background: linear-gradient(135deg, #0a1a33 0%, #1a3a66 100%);
        color: #ffffff;
        height: 100vh;
        overflow: hidden;
      }

      .container {
        width: 100%;
        height: 100vh;
        padding: 10px;
        display: flex;
        flex-direction: column;
      }

      .header {
        text-align: center;
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 10px;
      }

      .title {
        font-size: 18px;
        font-weight: bold;
        color: #d1d7db;
      }

      .chart-container {
        flex: 1;
        position: relative;
        background: rgba(10, 26, 51, 0.7);
        border: 1px solid #1a3a66;
        border-radius: 8px;
        padding: 10px;
      }

      #balance-chart {
        width: 100%;
        height: 100%;
      }

      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #6c7e9a;
        font-size: 14px;
      }

      .error {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ff6b6b;
        font-size: 14px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="chart-container">
        <div id="balance-chart"></div>
        <div class="loading" id="loading">正在加载图表...</div>
      </div>
    </div>

    <!-- 引入ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <script>
      // 平衡图表类
      class BalanceChart {
        constructor(containerId) {
          this.container = document.getElementById(containerId);
          this.chart = null;
          this.loadingElement = document.getElementById("loading");
          this.init();
        }

        init() {
          if (!this.container) {
            console.error("容器元素不存在");
            return;
          }

          // 初始化ECharts实例
          this.chart = echarts.init(this.container);

          // 生成模拟数据并渲染
          this.generateMockData();
          this.render();

          // 添加窗口大小变化的监听器
          window.addEventListener("resize", () => {
            if (this.chart) {
              this.chart.resize();
            }
          });
        }

        generateMockData() {
          // 生成24小时的时间轴
          const hours = [];
          for (let i = 0; i < 24; i++) {
            hours.push(String(i).padStart(2, "0") + ":00");
          }

          // 生成模拟的平衡数据
          const balanceData = [];
          const forecastData = [];

          for (let i = 0; i < 24; i++) {
            // 模拟实际平衡数据（前16小时）
            if (i < 16) {
              balanceData.push(
                (
                  Math.sin(i * 0.3) * 50 +
                  Math.random() * 20 -
                  10 +
                  100
                ).toFixed(1)
              );
              forecastData.push(null);
            } else {
              // 模拟预测数据（后8小时）
              balanceData.push(null);
              forecastData.push(
                (
                  Math.sin(i * 0.3) * 45 +
                  Math.random() * 15 -
                  7.5 +
                  95
                ).toFixed(1)
              );
            }
          }

          this.data = {
            xAxis: hours,
            series: [
              {
                name: "实际平衡",
                type: "line",
                data: balanceData,
                smooth: true,
                symbol: "circle",
                symbolSize: 6,
                lineStyle: {
                  color: "#5cefab",
                  width: 2,
                },
                itemStyle: {
                  color: "#5cefab",
                  borderColor: "rgba(92, 239, 171, 0.5)",
                  borderWidth: 4,
                },
              },
              {
                name: "预测平衡",
                type: "line",
                data: forecastData,
                smooth: true,
                symbol: "circle",
                symbolSize: 6,
                lineStyle: {
                  color: "#448df5",
                  width: 2,
                  type: "dashed",
                },
                itemStyle: {
                  color: "#448df5",
                  borderColor: "rgba(68, 141, 245, 0.5)",
                  borderWidth: 4,
                },
              },
            ],
          };
        }

        render() {
          if (!this.data || !this.chart) {
            return;
          }

          // 隐藏加载提示
          if (this.loadingElement) {
            this.loadingElement.style.display = "none";
          }

          const option = {
            backgroundColor: "transparent",
            grid: {
              left: "5%",
              right: "5%",
              bottom: "10%",
              top: "15%",
              containLabel: true,
            },
            tooltip: {
              trigger: "axis",
              backgroundColor: "rgba(10, 26, 51, 0.9)",
              borderColor: "#1a3a66",
              textStyle: {
                color: "#fff",
              },
              formatter: function (params) {
                let result = params[0].axisValue + "<br/>";
                params.forEach((param) => {
                  if (param.value !== null) {
                    result += `${param.marker} ${param.seriesName}: ${param.value} MW<br/>`;
                  }
                });
                return result;
              },
            },
            legend: {
              data: ["实际平衡", "预测平衡"],
              textStyle: {
                color: "#c0c0c0",
              },
              top: "0%",
              right: "0%",
              itemHeight: 8,
            },
            xAxis: {
              type: "category",
              data: this.data.xAxis,
              axisLine: {
                lineStyle: {
                  color: "#1a3a66",
                },
              },
              axisLabel: {
                color: "#6c7e9a",
              },
              splitLine: {
                show: false,
              },
            },
            yAxis: {
              type: "value",
              name: "MW",
              nameTextStyle: {
                color: "#d1d7db",
              },
              axisLine: {
                lineStyle: {
                  color: "#1a3a66",
                },
              },
              axisLabel: {
                color: "#6c7e9a",
              },
              splitLine: {
                lineStyle: {
                  color: "#1a3a66",
                  type: "dashed",
                },
              },
            },
            series: this.data.series,
          };

          this.chart.setOption(option);
        }

        destroy() {
          if (this.chart) {
            this.chart.dispose();
            this.chart = null;
          }
          window.removeEventListener("resize", this.chart?.resize);
        }
      }

      // 页面加载完成后初始化图表
      document.addEventListener("DOMContentLoaded", () => {
        const balanceChart = new BalanceChart("balance-chart");

        // 将图表实例挂载到全局对象，便于调试
        window.balanceChart = balanceChart;

        console.log("平衡图表测试页面初始化完成");
      });
    </script>
  </body>
</html>
