# 多页面应用配置指南

## 概述

本项目已配置为支持多页面应用，可以轻松添加和管理多个独立的页面。

## 当前页面

### 1. 主页面 (balance-monitoring)
- **文件**: `index.html`
- **路径**: `/` 或 `/index.html`
- **功能**: 全网平衡监视系统主页面
- **包含**: CPS图表、断面监视、信息显示等

### 2. 测试页面 (balance-chart-test)
- **文件**: `test.html`
- **路径**: `/test.html`
- **功能**: 平衡图表测试页面
- **包含**: 独立的平衡图表组件测试

### 3. 页面导航 (page-navigation)
- **文件**: `pages.html`
- **路径**: `/pages.html`
- **功能**: 页面导航和项目说明
- **包含**: 所有页面的链接和开发说明

## 开发命令

```bash
# 启动开发服务器（默认打开页面导航）
npm run dev

# 构建所有页面
npm run build

# 预览构建结果
npm run preview
```

## 添加新页面

### 1. 创建HTML文件
在项目根目录创建新的HTML文件，例如 `new-page.html`

### 2. 修改vite.config.js
在 `pages` 配置中添加新页面：

```javascript
const pages = {
  // 现有页面...
  
  // 新页面
  newPage: {
    entry: resolve(__dirname, "new-page.html"),
    name: "new-page-name",
    title: "新页面标题",
  },
};
```

### 3. 更新页面导航
在 `pages.html` 中的JavaScript部分添加新页面信息：

```javascript
const pages = {
  // 现有页面...
  
  newPage: {
    name: "new-page-name",
    title: "新页面标题",
    description: "新页面的描述信息",
    url: "./new-page.html"
  }
};
```

## 构建输出

构建后的文件结构：
```
dist/
├── index.html              # 主页面
├── test.html               # 测试页面
├── pages.html              # 页面导航
├── new-page.html           # 新添加的页面
├── js/
│   ├── main.[hash].js      # 主页面JS
│   ├── test.[hash].js      # 测试页面JS
│   ├── pages.[hash].js     # 页面导航JS
│   └── chunks/             # 共享代码块
└── assets/
    ├── css/                # 样式文件
    ├── images/             # 图片资源
    └── fonts/              # 字体文件
```

## 路径别名

项目配置了以下路径别名：
- `@`: 项目根目录
- `@js`: js目录
- `@css`: css目录  
- `@assets`: assets目录

使用示例：
```javascript
import CPSChart from "@js/components/CPSChart.js";
import "@css/style.css";
```

## 开发技巧

### 1. 快捷键
在页面导航中：
- `Ctrl+1`: 打开主页面
- `Ctrl+2`: 打开测试页面

### 2. 直接访问
开发环境下可以直接访问：
- `http://localhost:3000/` - 主页面
- `http://localhost:3000/test.html` - 测试页面
- `http://localhost:3000/pages.html` - 页面导航

### 3. 代码共享
- 共享的组件放在 `js/components/` 目录
- 共享的配置放在 `js/config/` 目录
- 共享的样式放在 `css/` 目录

## 注意事项

1. **页面独立性**: 每个页面都是独立的入口，有自己的JS和CSS
2. **代码分割**: Vite会自动进行代码分割，共享代码会被提取到chunks中
3. **资源优化**: 图片、字体等资源会被自动优化和哈希命名
4. **开发热更新**: 所有页面都支持热更新
5. **生产构建**: 所有页面都会被构建到dist目录

## 扩展建议

### 可能的新页面
- 风电监控页面
- 光伏监控页面
- 系统配置页面
- 数据分析页面
- 报表页面

### 页面间通信
如果需要页面间通信，可以考虑：
- LocalStorage
- SessionStorage
- URL参数
- PostMessage API
