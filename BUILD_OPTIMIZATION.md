# Vite 打包优化说明

## 优化内容

### 1. JS文件分离
- **入口文件**: 放在 `js/` 目录下，格式为 `js/[name].[hash].js`
- **第三方库**: 放在 `js/vendor/` 目录下，格式为 `js/vendor/[name].[hash].js`
- **其他代码块**: 放在 `js/chunks/` 目录下，格式为 `js/chunks/[name].[hash].js`

### 2. 静态资源分类
- **图片资源**: `assets/images/[name].[hash].[ext]`
  - 支持格式: png, jpg, jpeg, gif, svg, webp, ico
- **字体资源**: `assets/fonts/[name].[hash].[ext]`
  - 支持格式: woff, woff2, eot, ttf, otf
- **CSS文件**: `assets/css/[name].[hash].[ext]`
- **其他资源**: `assets/[name].[hash].[ext]`

### 3. 手动分包配置
- **vendor包**: 包含常用第三方库 (vue, vue-router, axios, lodash)
- 可根据项目需要添加更多分包策略

### 4. 生产环境优化
- 移除 console 和 debugger 语句
- 设置分包大小警告阈值为 1000KB
- 启用 Terser 压缩

## 打包后的目录结构

```
dist/
├── index.html
├── js/
│   ├── main.[hash].js          # 入口文件
│   ├── vendor/
│   │   └── vendor.[hash].js    # 第三方库
│   └── chunks/
│       └── [name].[hash].js    # 其他代码块
└── assets/
    ├── css/
    │   └── [name].[hash].css   # CSS文件
    ├── images/
    │   └── [name].[hash].[ext] # 图片资源
    ├── fonts/
    │   └── [name].[hash].[ext] # 字体资源
    └── [name].[hash].[ext]     # 其他资源
```

## 优化效果

1. **更好的缓存策略**: 第三方库和业务代码分离，第三方库变化频率低，可以更好地利用浏览器缓存
2. **并行加载**: 不同类型的资源可以并行下载，提升加载速度
3. **资源管理**: 静态资源按类型分类存放，便于管理和CDN部署
4. **代码分割**: 按需加载，减少初始包大小

## 使用方法

运行构建命令：
```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

构建完成后，所有文件将按照上述结构输出到 `dist` 目录。
