<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>页面导航 - 多页面应用</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        background: linear-gradient(135deg, #0a1a33 0%, #1a3a66 100%);
        color: #ffffff;
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      .header {
        text-align: center;
        padding: 40px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 40px;
      }

      .title {
        font-size: 32px;
        font-weight: bold;
        color: #d1d7db;
        margin-bottom: 10px;
      }

      .subtitle {
        font-size: 16px;
        color: #6c7e9a;
      }

      .pages-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
      }

      .page-card {
        background: rgba(10, 26, 51, 0.7);
        border: 1px solid #1a3a66;
        border-radius: 12px;
        padding: 24px;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .page-card:hover {
        border-color: #448df5;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(68, 141, 245, 0.2);
      }

      .page-title {
        font-size: 20px;
        font-weight: bold;
        color: #5cefab;
        margin-bottom: 8px;
      }

      .page-name {
        font-size: 14px;
        color: #6c7e9a;
        margin-bottom: 12px;
      }

      .page-description {
        font-size: 14px;
        color: #c0c0c0;
        line-height: 1.5;
        margin-bottom: 16px;
      }

      .page-link {
        display: inline-block;
        padding: 8px 16px;
        background: #448df5;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        font-size: 14px;
        transition: background 0.3s ease;
      }

      .page-link:hover {
        background: #357abd;
      }

      .info-section {
        background: rgba(10, 26, 51, 0.7);
        border: 1px solid #1a3a66;
        border-radius: 12px;
        padding: 24px;
        margin-top: 40px;
      }

      .info-title {
        font-size: 18px;
        color: #5cefab;
        margin-bottom: 16px;
      }

      .info-list {
        list-style: none;
      }

      .info-list li {
        padding: 8px 0;
        color: #c0c0c0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .info-list li:last-child {
        border-bottom: none;
      }

      .code {
        background: rgba(0, 0, 0, 0.3);
        padding: 2px 6px;
        border-radius: 4px;
        font-family: "Courier New", monospace;
        color: #5cefab;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="title">多页面应用导航</div>
        <div class="subtitle">Balance Monitoring Multi-Page Application</div>
      </div>

      <div class="pages-grid" id="pages-grid">
        <!-- 页面卡片将通过JavaScript动态生成 -->
      </div>

      <div class="info-section">
        <div class="info-title">开发说明</div>
        <ul class="info-list">
          <li>
            <strong>开发服务器:</strong> 运行
            <span class="code">npm run dev</span> 启动开发服务器
          </li>
          <li>
            <strong>构建项目:</strong> 运行
            <span class="code">npm run build</span> 构建所有页面
          </li>
          <li>
            <strong>预览构建:</strong> 运行
            <span class="code">npm run preview</span> 预览构建结果
          </li>
          <li>
            <strong>添加新页面:</strong> 在
            <span class="code">vite.config.js</span> 的
            <span class="code">pages</span> 配置中添加新页面
          </li>
          <li><strong>页面访问:</strong> 开发环境下可直接访问对应的HTML文件</li>
        </ul>
      </div>
    </div>

    <script>
      // 页面配置（与vite.config.js中的配置保持一致）
      const pages = {
        main: {
          name: "balance-monitoring",
          title: "全网平衡监视系统",
          description:
            "主要的平衡监视系统页面，包含CPS图表、断面监视、信息显示等功能模块。",
          url: "./balance-monitoring.html",
        },
        test: {
          name: "balance-chart-test",
          title: "平衡图表测试页面",
          description:
            "用于测试平衡图表功能的独立页面，包含模拟数据和交互功能。",
          url: "./test.html",
        },
      };

      // 动态生成页面卡片
      function generatePageCards() {
        const grid = document.getElementById("pages-grid");

        Object.keys(pages).forEach((key) => {
          const page = pages[key];
          const card = document.createElement("div");
          card.className = "page-card";
          card.onclick = () => window.open(page.url, "_blank");

          card.innerHTML = `
                    <div class="page-title">${page.title}</div>
                    <div class="page-name">页面标识: ${page.name}</div>
                    <div class="page-description">${page.description}</div>
                    <a href="${page.url}" class="page-link" onclick="event.stopPropagation()">访问页面</a>
                `;

          grid.appendChild(card);
        });
      }

      // 页面加载完成后生成卡片
      document.addEventListener("DOMContentLoaded", generatePageCards);

      // 添加键盘快捷键
      document.addEventListener("keydown", (e) => {
        if (e.ctrlKey || e.metaKey) {
          switch (e.key) {
            case "1":
              e.preventDefault();
              window.open("./balance-monitoring.html", "_blank");
              break;
            case "2":
              e.preventDefault();
              window.open("./test.html", "_blank");
              break;
          }
        }
      });

      console.log("🚀 多页面应用导航已加载");
      console.log("💡 快捷键: Ctrl+1 打开主页面, Ctrl+2 打开测试页面");
    </script>
  </body>
</html>
