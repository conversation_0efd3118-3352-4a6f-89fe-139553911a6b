# 构建结构说明

## 文件重命名

已将 `index.html` 重命名为 `balance-monitoring.html`，使页面名称更加明确。

## 开发环境访问

### 直接访问
- **主页面**: `http://localhost:3000/balance-monitoring.html`
- **测试页面**: `http://localhost:3000/test.html`
- **页面导航**: `http://localhost:3000/pages.html` (默认打开)

### 页面导航
运行 `npm run dev` 后会自动打开页面导航，可以方便地选择要访问的页面。

## 生产环境构建

### 构建命令
```bash
npm run build
```

### 构建后的目录结构
```
dist/
├── balance-monitoring/
│   └── index.html              # 主页面，通过 /balance-monitoring/ 访问
├── test.html                   # 测试页面，通过 /test.html 访问
├── pages.html                  # 页面导航，通过 /pages.html 访问
├── js/
│   ├── balance-monitoring/
│   │   └── index.[hash].js     # 主页面的JS文件
│   ├── test.[hash].js          # 测试页面的JS文件
│   ├── pages.[hash].js         # 页面导航的JS文件
│   └── chunks/                 # 共享代码块
│       └── vendor.[hash].js    # 第三方库
└── assets/
    ├── css/                    # 样式文件
    ├── images/                 # 图片资源
    └── fonts/                  # 字体文件
```

### 生产环境访问路径
- **主页面**: `https://your-domain.com/balance-monitoring/`
- **测试页面**: `https://your-domain.com/test.html`
- **页面导航**: `https://your-domain.com/pages.html`

## 配置说明

### vite.config.js 关键配置

```javascript
// 多页面配置
const pages = {
  main: {
    entry: resolve(__dirname, "balance-monitoring.html"),
    name: "balance-monitoring",
    title: "全网平衡监视系统",
  },
  // 其他页面...
};

// 构建输入配置
function buildMultiPageInput() {
  const input = {};
  Object.keys(pages).forEach((key) => {
    const page = pages[key];
    // 主页面输出到 balance-monitoring/index.html
    if (key === "main") {
      input["balance-monitoring/index"] = page.entry;
    } else {
      input[key] = page.entry;
    }
  });
  return input;
}
```

## 添加新页面

### 1. 创建HTML文件
例如创建 `wind-monitoring.html`

### 2. 更新vite.config.js
```javascript
const pages = {
  // 现有页面...
  windMonitoring: {
    entry: resolve(__dirname, "wind-monitoring.html"),
    name: "wind-monitoring",
    title: "风电监控系统",
  },
};

// 在buildMultiPageInput函数中添加路径配置
if (key === "windMonitoring") {
  input["wind-monitoring/index"] = page.entry;
}
```

### 3. 更新pages.html导航
在页面配置中添加新页面信息。

## 部署建议

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 主页面路由
    location /balance-monitoring/ {
        try_files $uri $uri/ /balance-monitoring/index.html;
    }

    # 其他页面
    location / {
        try_files $uri $uri/ =404;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/dist
    
    # 主页面路由
    <Directory "/path/to/dist/balance-monitoring">
        DirectoryIndex index.html
        AllowOverride All
        Require all granted
    </Directory>
    
    # 静态资源缓存
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

## 优势

1. **清晰的页面结构**: 每个页面都有明确的路径和名称
2. **独立的入口点**: 每个页面都是独立的应用入口
3. **代码分割**: 自动进行代码分割，优化加载性能
4. **扩展性**: 可以轻松添加新的页面
5. **SEO友好**: 每个页面都有独立的URL路径

## 注意事项

1. 开发时直接访问 `balance-monitoring.html`
2. 生产环境通过 `/balance-monitoring/` 路径访问主页面
3. 所有页面共享相同的静态资源目录
4. 新增页面需要同时更新vite配置和页面导航
